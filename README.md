# Dự án Khai thác Dữ liệu: Xu hướng Thời trang

## Mô tả dự án
Dự án này sử dụng DeepFashion Dataset để phân tích và dự đoán xu hướng thời trang thông qua các kỹ thuật khai thác dữ liệu và machine learning.

## Dataset
- **Tên**: DeepFashion Dataset
- **Nguồn**: http://mmlab.ie.cuhk.edu.hk/projects/DeepFashion.html
- **Kích thước**: Hơn 800,000 ảnh quần áo
- **Định dạng**: JPG/CSV
- **Chú thích**: 50 categories, 1,000 descriptive attributes, bounding boxes, clothing landmarks

## Cấu trúc dự án
```
XuHuongThoiTrang/
├── data/                    # Dữ liệu thô và đã xử lý
│   ├── raw/                # Dữ liệu gốc từ DeepFashion
│   ├── processed/          # Dữ liệu đã tiền xử lý
│   └── external/           # Dữ liệu bổ sung từ nguồn khác
├── notebooks/              # Jupyter notebooks cho EDA và thử nghiệm
├── src/                    # Source code chính
│   ├── data/              # Scripts xử lý dữ liệu
│   ├── features/          # Feature engineering
│   ├── models/            # Mô hình machine learning
│   ├── visualization/     # Trực quan hóa dữ liệu
│   └── utils/             # Utilities và helper functions
├── models/                # Trained models
├── reports/               # Báo cáo và kết quả
│   ├── figures/           # Biểu đồ và hình ảnh
│   └── final_report.md    # Báo cáo cuối cùng
├── dashboard/             # Web dashboard
├── requirements.txt       # Python dependencies
├── environment.yml        # Conda environment
└── config.yaml           # Configuration file
```

## Mục tiêu dự án
1. **Phân tích khám phá dữ liệu (EDA)**: Hiểu về phân bố các loại quần áo, thuộc tính
2. **Phân loại thời trang**: Xây dựng mô hình phân loại các item thời trang
3. **Dự đoán xu hướng**: Phát hiện và dự đoán xu hướng thời trang
4. **Hệ thống gợi ý**: Tạo recommendation system cho thời trang
5. **Dashboard**: Phát triển giao diện trực quan hóa kết quả

## Công nghệ sử dụng
- **Python**: Ngôn ngữ lập trình chính
- **Pandas, NumPy**: Xử lý dữ liệu
- **Scikit-learn**: Machine learning
- **TensorFlow/PyTorch**: Deep learning
- **Matplotlib, Seaborn**: Trực quan hóa
- **Streamlit/Dash**: Web dashboard
- **OpenCV**: Xử lý ảnh

## 🚀 Cách chạy dự án

### Bước 1: Cài đặt môi trường
```bash
# Tạo virtual environment
python -m venv fashion_env

# Kích hoạt environment (Windows)
fashion_env\Scripts\activate

# Cài đặt dependencies
pip install -r requirements.txt
```

### Bước 2: Chạy pipeline hoàn chỉnh
```bash
# 1. Tải và chuẩn bị dữ liệu
python src/data/download_dataset.py

# 2. Tiền xử lý dữ liệu
python src/data/preprocess.py

# 3. Phân tích khám phá dữ liệu
python src/visualization/simple_eda.py

# 4. Training mô hình
python src/models/fixed_classifier.py

# 5. Test hệ thống gợi ý
python src/models/recommendation_system.py

# 6. Khởi chạy dashboard
python run_dashboard.py
```

### Bước 3: Truy cập dashboard
- Mở trình duyệt và truy cập: http://localhost:8501
- Khám phá các tính năng phân tích và gợi ý thời trang

## 📊 Kết quả Đạt được

### ✅ Hoàn thành
- **Pipeline khai thác dữ liệu hoàn chỉnh**
- **5 mô hình machine learning** (Random Forest, SVM, Gradient Boosting, etc.)
- **Hệ thống gợi ý thời trang** với 4 loại gợi ý
- **Dashboard trực quan hóa tương tác** với 5 trang chức năng
- **Phân tích EDA chi tiết** với insights về xu hướng
- **Báo cáo kỹ thuật đầy đủ** và tài liệu hướng dẫn

### 📈 Metrics
- **Dataset**: 1,000 fashion items, 15 categories, 25+ features
- **Best Model**: SVM với accuracy 11.5% (validation)
- **Recommendation System**: Cosine similarity + K-means clustering
- **Dashboard**: 5 trang tương tác với Plotly charts

## 📁 Files quan trọng

- `reports/final_report.md` - Báo cáo chi tiết dự án
- `USAGE_GUIDE.md` - Hướng dẫn sử dụng
- `dashboard/app.py` - Web dashboard chính
- `src/models/fixed_classifier.py` - Mô hình phân loại
- `src/models/recommendation_system.py` - Hệ thống gợi ý
- `notebooks/01_exploratory_data_analysis.ipynb` - EDA notebook

## 🎯 Ứng dụng thực tế
- **E-commerce**: Gợi ý sản phẩm thời trang
- **Fashion Analytics**: Phân tích xu hướng thị trường
- **Personal Styling**: Tư vấn phong cách cá nhân
- **Inventory Management**: Dự đoán nhu cầu sản phẩm

## 👨‍💻 Tác giả
- **Môn học**: Khai thác Dữ liệu
- **Dataset**: DeepFashion (CUHK)
- **Công nghệ**: Python, Scikit-learn, Streamlit, Plotly
- **Hoàn thành**: 2025-10-04

## 📄 License
Dự án này chỉ dành cho mục đích học tập và nghiên cứu. Dataset DeepFashion thuộc bản quyền của MMLAB, CUHK.
