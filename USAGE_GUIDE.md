# Hướng dẫn Sử dụng - Dự án Xu hướng Thời trang

## 🚀 Bắt đầu <PERSON>

### 1. Cài đặt Môi trường

```bash
# Tạo virtual environment
python -m venv fashion_env

# Kích hoạt environment (Windows)
fashion_env\Scripts\activate

# Cài đặt dependencies
pip install -r requirements.txt
```

### 2. Chạy Pipeline Hoàn chỉnh

```bash
# Bước 1: Tải và chuẩn bị dữ liệu
python src/data/download_dataset.py

# Bước 2: Tiền xử lý dữ liệu
python src/data/preprocess.py

# Bước 3: Phân tích khám phá dữ liệu
python src/visualization/simple_eda.py

# Bước 4: Training mô hình
python src/models/fixed_classifier.py

# Bước 5: Test hệ thống gợi ý
python src/models/recommendation_system.py

# Bước 6: Khởi chạy dashboard
python run_dashboard.py
```

## 📊 Sử dụng Dashboard

### Khởi chạy Dashboard
```bash
python run_dashboard.py
```

Dashboard sẽ mở tại: http://localhost:8501

### Các Trang Dashboard

1. **📊 Tổng quan**
   - Metrics tổng thể
   - Phân bố loại quần áo
   - Top categories

2. **🎨 Phân tích màu sắc**
   - Pie chart phân bố màu
   - Heatmap màu theo category

3. **📈 Xu hướng**
   - Top trending categories
   - Insights xu hướng

4. **🔍 Gợi ý thời trang**
   - Chọn item để nhận gợi ý
   - Hiển thị items tương tự

5. **📏 Phân tích kích thước**
   - Histogram kích thước
   - Thống kê chi tiết

## 🔧 Sử dụng Các Module

### 1. Data Processing

```python
# Tải dữ liệu
from src.data.download_dataset import main as download_data
download_data()

# Tiền xử lý
from src.data.preprocess import main as preprocess_data
preprocess_data()
```

### 2. Exploratory Data Analysis

```python
# EDA đơn giản
from src.visualization.simple_eda import main as run_eda
run_eda()

# Hoặc sử dụng Jupyter notebook
jupyter notebook notebooks/01_exploratory_data_analysis.ipynb
```

### 3. Model Training

```python
# Training mô hình
from src.models.fixed_classifier import main as train_model
train_model()

# Sử dụng prediction service
from src.models.prediction_service import FashionPredictor

predictor = FashionPredictor()
result = predictor.predict(sample_data)
```

### 4. Recommendation System

```python
from src.models.recommendation_system import FashionRecommendationSystem

# Khởi tạo hệ thống
rec_system = FashionRecommendationSystem()
rec_system.load_data()
rec_system.prepare_features()
rec_system.build_similarity_matrix()

# Lấy gợi ý
similar_items = rec_system.get_similar_items(item_id=0, n_recommendations=5)
trend_items = rec_system.get_trend_recommendations(n_recommendations=10)
```

## 📁 Cấu trúc Dự án

```
XuHuongThoiTrang/
├── data/                    # Dữ liệu
│   ├── raw/                # Dữ liệu gốc
│   ├── processed/          # Dữ liệu đã xử lý
│   └── external/           # Dữ liệu bổ sung
├── src/                    # Source code
│   ├── data/              # Scripts xử lý dữ liệu
│   ├── models/            # Mô hình ML
│   ├── visualization/     # Trực quan hóa
│   └── utils/             # Utilities
├── notebooks/              # Jupyter notebooks
├── models/                # Trained models
├── reports/               # Báo cáo
├── dashboard/             # Web dashboard
├── config.yaml           # Cấu hình
├── requirements.txt       # Dependencies
└── run_dashboard.py       # Script chạy dashboard
```

## ⚙️ Cấu hình

### Config.yaml
Chỉnh sửa file `config.yaml` để thay đổi:
- Đường dẫn dữ liệu
- Hyperparameters mô hình
- Cài đặt dashboard

### Environment Variables
```bash
# Tùy chọn: Thiết lập biến môi trường
export FASHION_DATA_PATH=/path/to/data
export FASHION_MODEL_PATH=/path/to/models
```

## 🔍 Troubleshooting

### Lỗi thường gặp

1. **ModuleNotFoundError**
   ```bash
   # Cài đặt lại dependencies
   pip install -r requirements.txt
   ```

2. **FileNotFoundError**
   ```bash
   # Chạy lại data preparation
   python src/data/download_dataset.py
   ```

3. **Dashboard không khởi chạy**
   ```bash
   # Kiểm tra Streamlit
   pip install streamlit
   streamlit --version
   ```

4. **Model không load được**
   ```bash
   # Training lại model
   python src/models/fixed_classifier.py
   ```

### Performance Issues

1. **Chậm khi training**
   - Giảm số lượng estimators
   - Sử dụng n_jobs=-1 cho parallel processing

2. **Dashboard chậm**
   - Giảm kích thước dữ liệu hiển thị
   - Sử dụng caching (@st.cache_data)

## 📚 Tài liệu Tham khảo

### APIs chính

1. **FashionRecommendationSystem**
   - `load_data()`: Tải dữ liệu
   - `get_similar_items(item_id, n_recommendations)`: Gợi ý tương tự
   - `get_trend_recommendations(n_recommendations)`: Gợi ý xu hướng

2. **FashionPredictor**
   - `load_model(model_path)`: Load mô hình
   - `predict(data)`: Dự đoán
   - `predict_top_k(data, k)`: Top-k predictions

### Jupyter Notebooks

1. `01_exploratory_data_analysis.ipynb`: EDA chi tiết
2. Có thể thêm notebooks khác cho:
   - Feature engineering
   - Model comparison
   - Advanced visualizations

## 🎯 Use Cases

### 1. E-commerce Integration
```python
# Tích hợp vào hệ thống e-commerce
def get_product_recommendations(product_id):
    rec_system = FashionRecommendationSystem()
    rec_system.load_data()
    rec_system.prepare_features()
    rec_system.build_similarity_matrix()
    
    return rec_system.get_similar_items(product_id, n_recommendations=5)
```

### 2. Batch Processing
```python
# Xử lý hàng loạt
def batch_predict(data_file):
    predictor = FashionPredictor()
    data = pd.read_csv(data_file)
    
    results = []
    for idx, row in data.iterrows():
        result = predictor.predict(row)
        results.append(result)
    
    return results
```

### 3. API Service
```python
# Tạo API service với FastAPI
from fastapi import FastAPI

app = FastAPI()
predictor = FashionPredictor()

@app.post("/predict")
def predict_fashion(item_data: dict):
    return predictor.predict(item_data)

@app.get("/recommend/{item_id}")
def recommend_items(item_id: int):
    rec_system = FashionRecommendationSystem()
    # ... setup code ...
    return rec_system.get_similar_items(item_id)
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong terminal
2. Xem file README.md
3. Kiểm tra requirements.txt
4. Đảm bảo Python version >= 3.9

---

**Cập nhật lần cuối**: 2025-10-04  
**Version**: 1.0.0
