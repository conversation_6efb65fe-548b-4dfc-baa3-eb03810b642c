"""
Demo Script - Fashion Trend Analysis Project
Chạy script n<PERSON><PERSON> để test to<PERSON><PERSON> bộ hệ thống
"""

import subprocess
import sys
import time
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_step(step, description):
    """Print step information"""
    print(f"\n📋 Bước {step}: {description}")
    print("-" * 40)

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"⚡ Đang chạy: {description}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Thành công: {description}")
            return True
        else:
            print(f"❌ Lỗi: {description}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def check_environment():
    """Check if environment is set up correctly"""
    print_header("KIỂM TRA MÔI TRƯỜNG")
    
    # Check Python version
    python_version = sys.version
    print(f"🐍 Python version: {python_version}")
    
    # Check if virtual environment is activated
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment đã được kích hoạt")
    else:
        print("⚠️  Khuyến nghị: Sử dụng virtual environment")
    
    # Check required files
    required_files = [
        'config.yaml',
        'requirements.txt',
        'src/data/download_dataset.py',
        'src/data/preprocess.py',
        'src/visualization/simple_eda.py',
        'src/models/fixed_classifier.py',
        'src/models/recommendation_system.py',
        'dashboard/app.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Thiếu files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ Tất cả files cần thiết đều có sẵn")
        return True

def run_full_pipeline():
    """Run the complete data mining pipeline"""
    print_header("CHẠY PIPELINE HOÀN CHỈNH")
    
    steps = [
        ("python src/data/download_dataset.py", "Tải và chuẩn bị dữ liệu"),
        ("python src/data/preprocess.py", "Tiền xử lý dữ liệu"),
        ("python src/visualization/simple_eda.py", "Phân tích khám phá dữ liệu"),
        ("python src/models/fixed_classifier.py", "Training mô hình phân loại"),
        ("python src/models/recommendation_system.py", "Test hệ thống gợi ý")
    ]
    
    success_count = 0
    for i, (command, description) in enumerate(steps, 1):
        print_step(i, description)
        if run_command(command, description):
            success_count += 1
        time.sleep(1)  # Small delay between steps
    
    print(f"\n📊 Kết quả: {success_count}/{len(steps)} bước thành công")
    return success_count == len(steps)

def show_results_summary():
    """Show summary of results"""
    print_header("TÓM TẮT KẾT QUẢ")
    
    # Check if data files exist
    data_files = [
        "data/raw/sample_annotations.csv",
        "data/processed/X_train.csv",
        "data/processed/X_test.csv"
    ]
    
    print("📁 Dữ liệu:")
    for file_path in data_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
    
    # Check if model files exist
    model_files = [
        "models/fashion_classifier_svm_fixed.pkl",
        "models/model_metadata_fixed.pkl"
    ]
    
    print("\n🤖 Mô hình:")
    for file_path in model_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
    
    # Show project structure
    print("\n📂 Cấu trúc dự án:")
    important_dirs = ["data", "src", "models", "reports", "dashboard", "notebooks"]
    for dir_name in important_dirs:
        if Path(dir_name).exists():
            file_count = len(list(Path(dir_name).rglob("*")))
            print(f"   ✅ {dir_name}/ ({file_count} files)")
        else:
            print(f"   ❌ {dir_name}/")

def show_next_steps():
    """Show next steps for the user"""
    print_header("BƯỚC TIẾP THEO")
    
    print("🎯 Bạn có thể:")
    print("   1. Khởi chạy dashboard:")
    print("      python run_dashboard.py")
    print("      Truy cập: http://localhost:8501")
    
    print("\n   2. Xem báo cáo chi tiết:")
    print("      reports/final_report.md")
    
    print("\n   3. Đọc hướng dẫn sử dụng:")
    print("      USAGE_GUIDE.md")
    
    print("\n   4. Khám phá Jupyter notebook:")
    print("      jupyter notebook notebooks/01_exploratory_data_analysis.ipynb")
    
    print("\n   5. Tùy chỉnh cấu hình:")
    print("      config.yaml")

def main():
    """Main demo function"""
    print_header("DEMO - DỰ ÁN XU HƯỚNG THỜI TRANG")
    print("🎨 Fashion Trend Analysis với DeepFashion Dataset")
    print("📚 Môn: Khai thác Dữ liệu")
    print("🗓️  Ngày: 2025-10-04")
    
    # Check environment
    if not check_environment():
        print("\n❌ Môi trường chưa sẵn sàng. Vui lòng kiểm tra lại.")
        return
    
    # Ask user if they want to run full pipeline
    print("\n🤔 Bạn có muốn chạy toàn bộ pipeline? (y/n): ", end="")
    try:
        choice = input().lower().strip()
    except KeyboardInterrupt:
        print("\n👋 Tạm biệt!")
        return
    
    if choice in ['y', 'yes', 'có']:
        print("\n🚀 Bắt đầu chạy pipeline...")
        success = run_full_pipeline()
        
        if success:
            print("\n🎉 Pipeline hoàn thành thành công!")
        else:
            print("\n⚠️  Pipeline hoàn thành với một số lỗi")
    else:
        print("\n⏭️  Bỏ qua chạy pipeline")
    
    # Show results
    show_results_summary()
    
    # Show next steps
    show_next_steps()
    
    print_header("HOÀN THÀNH DEMO")
    print("✨ Cảm ơn bạn đã sử dụng hệ thống Fashion Trend Analysis!")
    print("📧 Nếu có vấn đề, vui lòng kiểm tra logs hoặc documentation")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Demo bị dừng bởi người dùng. Tạm biệt!")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {e}")
        print("📧 Vui lòng kiểm tra lại môi trường và dependencies")
