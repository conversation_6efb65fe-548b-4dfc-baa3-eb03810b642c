"""
Fashion Prediction Service
"""

import pandas as pd
import numpy as np
from pathlib import Path
import joblib
import yaml

class FashionPredictor:
    """Fashion item classifier and predictor"""
    
    def __init__(self, model_path=None):
        """Initialize the predictor"""
        self.model = None
        self.encoders = None
        self.scaler = None
        self.metadata = None
        self.feature_names = None
        
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path=None):
        """Load trained model and preprocessing objects"""
        models_dir = Path('models')
        processed_dir = Path('data/processed')
        
        if model_path is None:
            # Find the latest model
            model_files = list(models_dir.glob('fashion_classifier_*.pkl'))
            if not model_files:
                raise FileNotFoundError("No trained model found!")
            model_path = model_files[-1]  # Use the latest one
        
        print(f"Loading model from: {model_path}")
        
        # Load model
        self.model = joblib.load(model_path)
        
        # Load preprocessing objects
        self.encoders = joblib.load(processed_dir / 'encoders.pkl')
        self.scaler = joblib.load(processed_dir / 'scaler.pkl')
        
        # Load metadata
        metadata_file = models_dir / 'model_metadata_fixed.pkl'
        if metadata_file.exists():
            self.metadata = joblib.load(metadata_file)
            self.feature_names = self.metadata['feature_names']
        
        print(f"✓ Model loaded: {type(self.model).__name__}")
        print(f"✓ Features: {len(self.feature_names) if self.feature_names else 'Unknown'}")
        print(f"✓ Classes: {len(self.encoders['category'].classes_)}")
    
    def preprocess_input(self, data):
        """Preprocess input data for prediction"""
        if isinstance(data, dict):
            data = pd.DataFrame([data])
        elif isinstance(data, pd.Series):
            data = pd.DataFrame([data])
        
        # Ensure all required features are present
        if self.feature_names:
            missing_features = set(self.feature_names) - set(data.columns)
            if missing_features:
                print(f"Warning: Missing features: {missing_features}")
                # Fill missing features with 0
                for feature in missing_features:
                    data[feature] = 0
            
            # Select only required features in correct order
            data = data[self.feature_names]
        
        # Scale numerical features
        data_scaled = self.scaler.transform(data)
        
        return data_scaled
    
    def predict(self, data):
        """Make predictions on input data"""
        if self.model is None:
            raise ValueError("Model not loaded! Call load_model() first.")
        
        # Preprocess input
        X = self.preprocess_input(data)
        
        # Make predictions
        predictions = self.model.predict(X)
        probabilities = None
        
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(X)
        
        # Convert predictions to category names
        category_names = self.encoders['category'].inverse_transform(predictions)
        
        results = []
        for i, (pred, cat_name) in enumerate(zip(predictions, category_names)):
            result = {
                'predicted_category': cat_name,
                'predicted_id': int(pred),
                'confidence': None
            }
            
            if probabilities is not None:
                result['confidence'] = float(np.max(probabilities[i]))
                result['all_probabilities'] = {
                    self.encoders['category'].classes_[j]: float(probabilities[i][j])
                    for j in range(len(self.encoders['category'].classes_))
                }
            
            results.append(result)
        
        return results if len(results) > 1 else results[0]
    
    def predict_top_k(self, data, k=3):
        """Get top-k predictions with probabilities"""
        if self.model is None:
            raise ValueError("Model not loaded! Call load_model() first.")
        
        if not hasattr(self.model, 'predict_proba'):
            return self.predict(data)
        
        # Preprocess input
        X = self.preprocess_input(data)
        
        # Get probabilities
        probabilities = self.model.predict_proba(X)
        
        results = []
        for i in range(len(X)):
            # Get top-k predictions
            top_k_indices = np.argsort(probabilities[i])[-k:][::-1]
            
            top_predictions = []
            for idx in top_k_indices:
                category_name = self.encoders['category'].classes_[idx]
                confidence = float(probabilities[i][idx])
                top_predictions.append({
                    'category': category_name,
                    'confidence': confidence
                })
            
            results.append({
                'top_predictions': top_predictions,
                'best_prediction': top_predictions[0]['category']
            })
        
        return results if len(results) > 1 else results[0]

def create_sample_predictions():
    """Create sample predictions for demonstration"""
    print("Creating sample predictions...")
    
    # Load some test data
    processed_dir = Path('data/processed')
    X_test = pd.read_csv(processed_dir / 'X_test.csv')
    y_test = pd.read_csv(processed_dir / 'y_test.csv')['category_encoded']
    
    # Initialize predictor
    predictor = FashionPredictor()
    
    # Make predictions on first 5 test samples
    sample_data = X_test.head(5)
    
    print(f"\n{'='*60}")
    print("SAMPLE PREDICTIONS")
    print("="*60)
    
    for i in range(len(sample_data)):
        sample = sample_data.iloc[i:i+1]
        actual_category = predictor.encoders['category'].inverse_transform([y_test.iloc[i]])[0]
        
        # Single prediction
        result = predictor.predict(sample)
        
        # Top-3 predictions
        top_k_result = predictor.predict_top_k(sample, k=3)
        
        print(f"\nSample {i+1}:")
        print(f"  Actual: {actual_category}")
        print(f"  Predicted: {result['predicted_category']}")
        if result['confidence']:
            print(f"  Confidence: {result['confidence']:.3f}")
        
        print(f"  Top 3 predictions:")
        for j, pred in enumerate(top_k_result['top_predictions'], 1):
            print(f"    {j}. {pred['category']}: {pred['confidence']:.3f}")

def analyze_model_performance():
    """Analyze model performance on test set"""
    print("Analyzing model performance...")
    
    # Load test data
    processed_dir = Path('data/processed')
    X_test = pd.read_csv(processed_dir / 'X_test.csv')
    y_test = pd.read_csv(processed_dir / 'y_test.csv')['category_encoded']
    
    # Initialize predictor
    predictor = FashionPredictor()
    
    # Make predictions on all test data
    predictions = []
    for i in range(len(X_test)):
        sample = X_test.iloc[i:i+1]
        result = predictor.predict(sample)
        predictions.append(result['predicted_id'])
    
    # Calculate accuracy
    accuracy = np.mean(np.array(predictions) == y_test.values)
    
    print(f"\n{'='*40}")
    print("MODEL PERFORMANCE ANALYSIS")
    print("="*40)
    print(f"Test Accuracy: {accuracy:.4f}")
    
    # Per-class analysis
    unique_classes = np.unique(y_test)
    print(f"\nPer-class Performance:")
    
    for class_id in unique_classes:
        class_name = predictor.encoders['category'].inverse_transform([class_id])[0]
        class_mask = y_test == class_id
        class_predictions = np.array(predictions)[class_mask]
        class_accuracy = np.mean(class_predictions == class_id)
        class_count = np.sum(class_mask)
        
        print(f"  {class_name:<15}: {class_accuracy:.4f} ({class_count} samples)")

def main():
    """Main prediction service demo"""
    print("Fashion Prediction Service Demo")
    print("="*40)
    
    try:
        # Create sample predictions
        create_sample_predictions()
        
        # Analyze model performance
        analyze_model_performance()
        
        print(f"\n{'='*60}")
        print("✓ Prediction service demo completed!")
        print("✓ Model is ready for making predictions on new fashion items")
        print("="*60)
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have trained a model first by running:")
        print("  python src/models/fixed_classifier.py")

if __name__ == "__main__":
    main()
