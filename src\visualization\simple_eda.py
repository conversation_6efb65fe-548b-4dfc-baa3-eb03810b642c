"""
Simple EDA without plotting for command line execution
"""

import pandas as pd
import numpy as np
from pathlib import Path
import yaml

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def load_data():
    """Load raw data for analysis"""
    raw_data_path = Path('data/raw')
    df = pd.read_csv(raw_data_path / 'sample_annotations.csv')
    return df

def analyze_basic_stats(df):
    """Analyze basic dataset statistics"""
    print("="*60)
    print("BÁO CÁO PHÂN TÍCH DỮ LIỆU CƠ BẢN")
    print("="*60)
    
    print(f"Tổng số mẫu: {len(df):,}")
    print(f"Số thuộc tính: {len(df.columns)}")
    print(f"Dung lượng bộ nhớ: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    print(f"\nKiểu dữ liệu:")
    dtype_counts = df.dtypes.value_counts()
    for dtype, count in dtype_counts.items():
        print(f"  {dtype}: {count} cột")
    
    missing_values = df.isnull().sum().sum()
    print(f"\nGiá trị thiếu: {missing_values}")
    if missing_values == 0:
        print("  ✓ Không có giá trị thiếu")

def analyze_categories(df):
    """Analyze category distribution"""
    print("\n" + "="*40)
    print("PHÂN TÍCH LOẠI QUẦN ÁO")
    print("="*40)
    
    category_counts = df['category'].value_counts()
    print(f"Số loại quần áo: {len(category_counts)}")
    
    print("\nTop 10 loại quần áo phổ biến:")
    for i, (category, count) in enumerate(category_counts.head(10).items(), 1):
        percentage = (count / len(df)) * 100
        print(f"  {i:2d}. {category:<15}: {count:3d} ({percentage:5.1f}%)")
    
    # Phân tích độ cân bằng
    max_count = category_counts.max()
    min_count = category_counts.min()
    balance_ratio = min_count / max_count
    print(f"\nĐộ cân bằng dataset: {balance_ratio:.3f}")
    if balance_ratio > 0.8:
        print("  ✓ Dataset cân bằng tốt")
    elif balance_ratio > 0.5:
        print("  ⚠ Dataset tương đối cân bằng")
    else:
        print("  ✗ Dataset không cân bằng")

def analyze_attributes(df):
    """Analyze fashion attributes"""
    print("\n" + "="*40)
    print("PHÂN TÍCH THUỘC TÍNH THỜI TRANG")
    print("="*40)
    
    # Color analysis
    color_cols = [col for col in df.columns if col.startswith('color_')]
    color_data = df[color_cols].sum().sort_values(ascending=False)
    
    print(f"Số thuộc tính màu sắc: {len(color_cols)}")
    print("Top 5 màu phổ biến:")
    for i, (color, count) in enumerate(color_data.head().items(), 1):
        color_name = color.replace('color_', '')
        percentage = (count / len(df)) * 100
        print(f"  {i}. {color_name:<10}: {count:3d} ({percentage:5.1f}%)")
    
    # Pattern analysis
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    pattern_data = df[pattern_cols].sum().sort_values(ascending=False)
    
    print(f"\nSố thuộc tính họa tiết: {len(pattern_cols)}")
    print("Top 5 họa tiết phổ biến:")
    for i, (pattern, count) in enumerate(pattern_data.head().items(), 1):
        pattern_name = pattern.replace('pattern_', '')
        percentage = (count / len(df)) * 100
        print(f"  {i}. {pattern_name:<10}: {count:3d} ({percentage:5.1f}%)")
    
    # Other attributes
    other_prefixes = ['sleeve_', 'neckline_', 'length_', 'fit_']
    for prefix in other_prefixes:
        cols = [col for col in df.columns if col.startswith(prefix)]
        if cols:
            data = df[cols].sum().sort_values(ascending=False)
            print(f"\nThuộc tính {prefix[:-1]}:")
            for attr, count in data.head(3).items():
                attr_name = attr.replace(prefix, '')
                percentage = (count / len(df)) * 100
                print(f"  {attr_name:<12}: {count:3d} ({percentage:5.1f}%)")

def analyze_bounding_boxes(df):
    """Analyze bounding box characteristics"""
    print("\n" + "="*40)
    print("PHÂN TÍCH BOUNDING BOX")
    print("="*40)
    
    # Calculate derived features
    df['aspect_ratio'] = df['bbox_width'] / df['bbox_height']
    df['bbox_area'] = df['bbox_width'] * df['bbox_height']
    
    bbox_stats = {
        'Chiều rộng': df['bbox_width'],
        'Chiều cao': df['bbox_height'],
        'Tỷ lệ khung hình': df['aspect_ratio'],
        'Diện tích': df['bbox_area']
    }
    
    for name, series in bbox_stats.items():
        print(f"\n{name}:")
        print(f"  Trung bình: {series.mean():8.1f}")
        print(f"  Độ lệch chuẩn: {series.std():8.1f}")
        print(f"  Min: {series.min():8.1f}")
        print(f"  Max: {series.max():8.1f}")
        print(f"  Median: {series.median():8.1f}")

def analyze_feature_engineering(df):
    """Analyze engineered features"""
    print("\n" + "="*40)
    print("PHÂN TÍCH FEATURE ENGINEERING")
    print("="*40)
    
    # Create composite features
    color_cols = [col for col in df.columns if col.startswith('color_')]
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    attribute_cols = [col for col in df.columns if any(
        col.startswith(prefix) for prefix in ['color_', 'pattern_', 'sleeve_', 'neckline_', 'length_', 'fit_']
    )]
    
    df['color_count'] = df[color_cols].sum(axis=1)
    df['pattern_count'] = df[pattern_cols].sum(axis=1)
    df['total_attributes'] = df[attribute_cols].sum(axis=1)
    
    print(f"Trung bình số màu mỗi item: {df['color_count'].mean():.2f}")
    print(f"Trung bình số họa tiết mỗi item: {df['pattern_count'].mean():.2f}")
    print(f"Trung bình tổng thuộc tính mỗi item: {df['total_attributes'].mean():.2f}")
    
    # Analyze by category
    print(f"\nPhân tích theo loại quần áo:")
    category_analysis = df.groupby('category').agg({
        'total_attributes': 'mean',
        'color_count': 'mean',
        'pattern_count': 'mean',
        'bbox_area': 'mean'
    }).round(2)
    
    print(f"{'Loại':<15} {'Tổng attr':<10} {'Màu':<6} {'Họa tiết':<8} {'Diện tích':<10}")
    print("-" * 55)
    for category, row in category_analysis.head(10).iterrows():
        print(f"{category:<15} {row['total_attributes']:<10} {row['color_count']:<6} {row['pattern_count']:<8} {row['bbox_area']:<10.0f}")

def generate_insights(df):
    """Generate key insights"""
    print("\n" + "="*60)
    print("INSIGHTS VÀ KẾT LUẬN")
    print("="*60)
    
    # Most popular items
    most_popular_category = df['category'].mode().iloc[0]
    color_cols = [col for col in df.columns if col.startswith('color_')]
    most_popular_color = df[color_cols].sum().idxmax().replace('color_', '')
    
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    most_popular_pattern = df[pattern_cols].sum().idxmax().replace('pattern_', '')
    
    print(f"1. Loại quần áo phổ biến nhất: {most_popular_category}")
    print(f"2. Màu sắc phổ biến nhất: {most_popular_color}")
    print(f"3. Họa tiết phổ biến nhất: {most_popular_pattern}")
    
    # Size analysis
    avg_area = df['bbox_width'] * df['bbox_height']
    print(f"4. Diện tích trung bình: {avg_area.mean():.0f} pixels²")
    
    # Diversity analysis
    category_diversity = len(df['category'].unique())
    total_categories = len(df['category'].unique())
    print(f"5. Độ đa dạng loại quần áo: {category_diversity}/{total_categories}")
    
    print(f"\n✓ Dataset phù hợp cho:")
    print(f"  - Phân loại thời trang ({len(df['category'].unique())} classes)")
    print(f"  - Phân tích xu hướng màu sắc và họa tiết")
    print(f"  - Dự đoán thuộc tính dựa trên hình ảnh")
    print(f"  - Hệ thống gợi ý thời trang")

def main():
    """Main EDA function"""
    print("Bắt đầu phân tích dữ liệu khám phá (EDA)...")
    
    # Load data
    df = load_data()
    
    # Run analyses
    analyze_basic_stats(df)
    analyze_categories(df)
    analyze_attributes(df)
    analyze_bounding_boxes(df)
    analyze_feature_engineering(df)
    generate_insights(df)
    
    print(f"\n{'='*60}")
    print("✓ EDA hoàn thành!")
    print("✓ Dữ liệu đã sẵn sàng cho việc xây dựng mô hình machine learning")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
