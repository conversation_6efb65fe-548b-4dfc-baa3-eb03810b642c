"""
<PERSON>ript to run the Fashion Trend Analysis Dashboard
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run the Streamlit dashboard"""
    print("🚀 Starting Fashion Trend Analysis Dashboard...")
    print("📊 Dashboard will open in your web browser")
    print("🔗 URL: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the dashboard")
    print("-" * 50)
    
    # Path to the dashboard app
    dashboard_path = Path(__file__).parent / "dashboard" / "app.py"
    
    # Run streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            str(dashboard_path),
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error running dashboard: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure Streamlit is installed: pip install streamlit")
        print("2. Check if the dashboard file exists")
        print("3. Ensure all dependencies are installed")

if __name__ == "__main__":
    main()
