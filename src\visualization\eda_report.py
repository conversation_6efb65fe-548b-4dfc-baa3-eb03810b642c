"""
Exploratory Data Analysis Report Generator
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import yaml
import warnings
warnings.filterwarnings('ignore')

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def setup_plotting():
    """Setup plotting style and parameters"""
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 12

def load_data():
    """Load raw data for analysis"""
    raw_data_path = Path('data/raw')
    df = pd.read_csv(raw_data_path / 'sample_annotations.csv')
    return df

def analyze_categories(df, save_path):
    """Analyze category distribution"""
    print("=== PHÂN TÍCH PHÂN BỐ LOẠI QUẦN ÁO ===")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Bar chart
    category_counts = df['category'].value_counts()
    category_counts.plot(kind='bar', ax=ax1, color='skyblue')
    ax1.set_title('Phân bố Loại Quần áo', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Loại quần áo')
    ax1.set_ylabel('Số lượng')
    ax1.tick_params(axis='x', rotation=45)
    
    # Pie chart
    ax2.pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%', startangle=90)
    ax2.set_title('Tỷ lệ Loại Quần áo', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(save_path / 'category_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Top 5 loại quần áo phổ biến nhất:")
    for category, count in category_counts.head().items():
        percentage = (count / len(df)) * 100
        print(f"  {category}: {count} ({percentage:.1f}%)")

def analyze_colors(df, save_path):
    """Analyze color attributes"""
    print("\n=== PHÂN TÍCH THUỘC TÍNH MÀU SẮC ===")
    
    color_cols = [col for col in df.columns if col.startswith('color_')]
    color_data = df[color_cols].sum().sort_values(ascending=False)
    
    plt.figure(figsize=(12, 6))
    colors = ['red', 'blue', 'black', 'lightgray', 'green']
    color_data.plot(kind='bar', color=colors[:len(color_data)])
    plt.title('Phân bố Thuộc tính Màu sắc', fontsize=14, fontweight='bold')
    plt.xlabel('Màu sắc')
    plt.ylabel('Số lượng item')
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(save_path / 'color_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Thống kê màu sắc:")
    for color, count in color_data.items():
        percentage = (count / len(df)) * 100
        print(f"  {color}: {count} items ({percentage:.1f}%)")

def analyze_patterns(df, save_path):
    """Analyze pattern attributes"""
    print("\n=== PHÂN TÍCH THUỘC TÍNH HỌA TIẾT ===")
    
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    pattern_data = df[pattern_cols].sum().sort_values(ascending=False)
    
    plt.figure(figsize=(10, 6))
    pattern_data.plot(kind='bar', color='lightcoral')
    plt.title('Phân bố Thuộc tính Họa tiết', fontsize=14, fontweight='bold')
    plt.xlabel('Họa tiết')
    plt.ylabel('Số lượng item')
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(save_path / 'pattern_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Thống kê họa tiết:")
    for pattern, count in pattern_data.items():
        percentage = (count / len(df)) * 100
        print(f"  {pattern}: {count} items ({percentage:.1f}%)")

def analyze_bounding_boxes(df, save_path):
    """Analyze bounding box characteristics"""
    print("\n=== PHÂN TÍCH BOUNDING BOX ===")
    
    # Calculate derived features
    df['aspect_ratio'] = df['bbox_width'] / df['bbox_height']
    df['bbox_area'] = df['bbox_width'] * df['bbox_height']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Width distribution
    axes[0,0].hist(df['bbox_width'], bins=30, alpha=0.7, color='skyblue')
    axes[0,0].set_title('Phân bố Chiều rộng Bounding Box')
    axes[0,0].set_xlabel('Chiều rộng (pixels)')
    axes[0,0].set_ylabel('Tần suất')
    
    # Height distribution
    axes[0,1].hist(df['bbox_height'], bins=30, alpha=0.7, color='lightcoral')
    axes[0,1].set_title('Phân bố Chiều cao Bounding Box')
    axes[0,1].set_xlabel('Chiều cao (pixels)')
    axes[0,1].set_ylabel('Tần suất')
    
    # Aspect ratio
    axes[1,0].hist(df['aspect_ratio'], bins=30, alpha=0.7, color='lightgreen')
    axes[1,0].set_title('Phân bố Tỷ lệ Khung hình')
    axes[1,0].set_xlabel('Tỷ lệ (width/height)')
    axes[1,0].set_ylabel('Tần suất')
    
    # Area
    axes[1,1].hist(df['bbox_area'], bins=30, alpha=0.7, color='gold')
    axes[1,1].set_title('Phân bố Diện tích Bounding Box')
    axes[1,1].set_xlabel('Diện tích (pixels²)')
    axes[1,1].set_ylabel('Tần suất')
    
    plt.tight_layout()
    plt.savefig(save_path / 'bbox_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Thống kê Bounding Box:")
    print(f"  Chiều rộng trung bình: {df['bbox_width'].mean():.1f} ± {df['bbox_width'].std():.1f}")
    print(f"  Chiều cao trung bình: {df['bbox_height'].mean():.1f} ± {df['bbox_height'].std():.1f}")
    print(f"  Tỷ lệ khung hình trung bình: {df['aspect_ratio'].mean():.2f} ± {df['aspect_ratio'].std():.2f}")
    print(f"  Diện tích trung bình: {df['bbox_area'].mean():.0f} ± {df['bbox_area'].std():.0f}")

def analyze_correlations(df, save_path):
    """Analyze correlations between features"""
    print("\n=== PHÂN TÍCH TƯƠNG QUAN ===")
    
    # Select numeric columns
    numeric_cols = ['bbox_x', 'bbox_y', 'bbox_width', 'bbox_height', 'aspect_ratio', 'bbox_area']
    correlation_matrix = df[numeric_cols].corr()
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, linewidths=0.5)
    plt.title('Ma trận Tương quan - Thuộc tính Hình học', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path / 'correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Các cặp thuộc tính có tương quan cao (|r| > 0.5):")
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_val = correlation_matrix.iloc[i, j]
            if abs(corr_val) > 0.5:
                print(f"  {correlation_matrix.columns[i]} - {correlation_matrix.columns[j]}: {corr_val:.3f}")

def generate_summary_report(df):
    """Generate summary statistics report"""
    print("\n" + "="*60)
    print("BÁO CÁO TỔNG KẾT PHÂN TÍCH DỮ LIỆU")
    print("="*60)
    
    print(f"Tổng số mẫu: {len(df):,}")
    print(f"Số loại quần áo: {df['category'].nunique()}")
    print(f"Số thuộc tính: {len(df.columns)}")
    
    # Feature engineering
    color_cols = [col for col in df.columns if col.startswith('color_')]
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    
    df['color_count'] = df[color_cols].sum(axis=1)
    df['pattern_count'] = df[pattern_cols].sum(axis=1)
    
    print(f"\nTrung bình số màu mỗi item: {df['color_count'].mean():.2f}")
    print(f"Trung bình số họa tiết mỗi item: {df['pattern_count'].mean():.2f}")
    
    print("\nLoại quần áo phổ biến nhất:", df['category'].mode().iloc[0])
    print("Màu phổ biến nhất:", df[color_cols].sum().idxmax().replace('color_', ''))
    print("Họa tiết phổ biến nhất:", df[pattern_cols].sum().idxmax().replace('pattern_', ''))

def main():
    """Main EDA function"""
    print("Bắt đầu phân tích dữ liệu khám phá (EDA)...")
    
    # Setup
    setup_plotting()
    config = load_config()
    
    # Create output directory
    output_path = Path('reports/figures')
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Load data
    df = load_data()
    print(f"Đã tải {len(df)} mẫu dữ liệu")
    
    # Run analyses
    analyze_categories(df, output_path)
    analyze_colors(df, output_path)
    analyze_patterns(df, output_path)
    analyze_bounding_boxes(df, output_path)
    analyze_correlations(df, output_path)
    generate_summary_report(df)
    
    print(f"\n✓ EDA hoàn thành! Các biểu đồ đã được lưu tại: {output_path}")
    print("✓ Dữ liệu đã sẵn sàng cho việc xây dựng mô hình machine learning")

if __name__ == "__main__":
    main()
