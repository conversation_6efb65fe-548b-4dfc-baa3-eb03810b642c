"""
Fashion Classification Models
"""

import pandas as pd
import numpy as np
from pathlib import Path
import yaml
import joblib
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.model_selection import cross_val_score, GridSearchCV
import warnings
warnings.filterwarnings('ignore')

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def load_processed_data():
    """Load preprocessed data"""
    processed_path = Path('data/processed')
    
    # Load data splits
    X_train = pd.read_csv(processed_path / 'X_train.csv')
    X_val = pd.read_csv(processed_path / 'X_val.csv')
    X_test = pd.read_csv(processed_path / 'X_test.csv')
    
    y_train = pd.read_csv(processed_path / 'y_train.csv')['category_encoded']
    y_val = pd.read_csv(processed_path / 'y_val.csv')['category_encoded']
    y_test = pd.read_csv(processed_path / 'y_test.csv')['category_encoded']
    
    # Load preprocessing objects
    encoders = joblib.load(processed_path / 'encoders.pkl')
    scaler = joblib.load(processed_path / 'scaler.pkl')
    
    print(f"Loaded data - Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
    
    return (X_train, X_val, X_test), (y_train, y_val, y_test), encoders, scaler

def create_models():
    """Create different ML models for comparison"""
    models = {
        'Random Forest': RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        ),
        'Gradient Boosting': GradientBoostingClassifier(
            n_estimators=100,
            max_depth=6,
            random_state=42
        ),
        'Logistic Regression': LogisticRegression(
            max_iter=1000,
            random_state=42
        ),
        'SVM': SVC(
            kernel='rbf',
            random_state=42,
            probability=True
        ),
        'K-Nearest Neighbors': KNeighborsClassifier(
            n_neighbors=5
        )
    }
    
    return models

def train_and_evaluate_models(X_train, X_val, y_train, y_val, models):
    """Train and evaluate all models"""
    results = {}
    trained_models = {}
    
    print("Training and evaluating models...")
    print("="*60)
    
    for name, model in models.items():
        print(f"\nTraining {name}...")
        
        # Train model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred_train = model.predict(X_train)
        y_pred_val = model.predict(X_val)
        
        # Calculate accuracies
        train_acc = accuracy_score(y_train, y_pred_train)
        val_acc = accuracy_score(y_val, y_pred_val)
        
        # Cross-validation score
        cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
        cv_mean = cv_scores.mean()
        cv_std = cv_scores.std()
        
        # Store results
        results[name] = {
            'train_accuracy': train_acc,
            'val_accuracy': val_acc,
            'cv_mean': cv_mean,
            'cv_std': cv_std,
            'predictions': y_pred_val
        }
        
        trained_models[name] = model
        
        print(f"  Train Accuracy: {train_acc:.4f}")
        print(f"  Val Accuracy: {val_acc:.4f}")
        print(f"  CV Score: {cv_mean:.4f} ± {cv_std:.4f}")
    
    return results, trained_models

def select_best_model(results):
    """Select the best model based on validation accuracy"""
    best_model_name = max(results.keys(), key=lambda x: results[x]['val_accuracy'])
    best_score = results[best_model_name]['val_accuracy']
    
    print(f"\n{'='*60}")
    print("MODEL COMPARISON RESULTS")
    print("="*60)
    
    print(f"{'Model':<20} {'Train Acc':<12} {'Val Acc':<12} {'CV Score':<15}")
    print("-" * 65)
    
    for name, result in results.items():
        marker = "★" if name == best_model_name else " "
        print(f"{marker} {name:<18} {result['train_accuracy']:<11.4f} "
              f"{result['val_accuracy']:<11.4f} "
              f"{result['cv_mean']:.4f}±{result['cv_std']:.3f}")
    
    print(f"\n✓ Best Model: {best_model_name} (Val Accuracy: {best_score:.4f})")
    
    return best_model_name

def detailed_evaluation(model, X_test, y_test, encoders, model_name):
    """Perform detailed evaluation on test set"""
    print(f"\n{'='*60}")
    print(f"DETAILED EVALUATION - {model_name}")
    print("="*60)
    
    # Make predictions
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
    
    # Calculate accuracy
    test_acc = accuracy_score(y_test, y_pred)
    print(f"Test Accuracy: {test_acc:.4f}")
    
    # Classification report
    category_encoder = encoders['category']
    target_names = category_encoder.classes_
    
    print(f"\nClassification Report:")
    print(classification_report(y_test, y_pred, target_names=target_names))
    
    # Confusion matrix analysis
    cm = confusion_matrix(y_test, y_pred)
    print(f"\nConfusion Matrix Shape: {cm.shape}")
    
    # Top predictions analysis
    if y_pred_proba is not None:
        print(f"\nTop 5 Most Confident Predictions:")
        max_probas = np.max(y_pred_proba, axis=1)
        top_indices = np.argsort(max_probas)[-5:][::-1]
        
        for i, idx in enumerate(top_indices, 1):
            predicted_class = category_encoder.inverse_transform([y_pred[idx]])[0]
            confidence = max_probas[idx]
            actual_class = category_encoder.inverse_transform([y_test.iloc[idx]])[0]
            correct = "✓" if predicted_class == actual_class else "✗"
            print(f"  {i}. {predicted_class} ({confidence:.3f}) - Actual: {actual_class} {correct}")
    
    return test_acc, y_pred

def feature_importance_analysis(model, feature_names, model_name):
    """Analyze feature importance for tree-based models"""
    if hasattr(model, 'feature_importances_'):
        print(f"\n{'='*40}")
        print(f"FEATURE IMPORTANCE - {model_name}")
        print("="*40)
        
        importances = model.feature_importances_
        feature_importance = pd.DataFrame({
            'feature': feature_names,
            'importance': importances
        }).sort_values('importance', ascending=False)
        
        print("Top 10 Most Important Features:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows(), 1):
            print(f"  {i:2d}. {row['feature']:<20}: {row['importance']:.4f}")
        
        return feature_importance
    else:
        print(f"\nFeature importance not available for {model_name}")
        return None

def save_best_model(model, model_name, encoders, scaler, feature_names):
    """Save the best model and preprocessing objects"""
    models_path = Path('models')
    models_path.mkdir(exist_ok=True)
    
    # Save model
    model_file = models_path / f'best_fashion_classifier_{model_name.lower().replace(" ", "_")}.pkl'
    joblib.dump(model, model_file)
    
    # Save model metadata
    metadata = {
        'model_name': model_name,
        'model_type': type(model).__name__,
        'feature_names': feature_names,
        'n_features': len(feature_names),
        'n_classes': len(encoders['category'].classes_),
        'classes': list(encoders['category'].classes_)
    }
    
    metadata_file = models_path / 'model_metadata.pkl'
    joblib.dump(metadata, metadata_file)
    
    print(f"\n✓ Model saved: {model_file}")
    print(f"✓ Metadata saved: {metadata_file}")
    print(f"✓ Encoders and scaler already saved in data/processed/")

def main():
    """Main training pipeline"""
    print("Starting Fashion Classification Model Training...")
    
    # Load configuration
    config = load_config()
    
    # Load processed data
    (X_train, X_val, X_test), (y_train, y_val, y_test), encoders, scaler = load_processed_data()
    
    # Get feature names
    feature_names = list(X_train.columns)
    
    # Create models
    models = create_models()
    
    # Train and evaluate models
    results, trained_models = train_and_evaluate_models(X_train, X_val, y_train, y_val, models)
    
    # Select best model
    best_model_name = select_best_model(results)
    best_model = trained_models[best_model_name]
    
    # Detailed evaluation on test set
    test_acc, y_pred = detailed_evaluation(best_model, X_test, y_test, encoders, best_model_name)
    
    # Feature importance analysis
    feature_importance = feature_importance_analysis(best_model, feature_names, best_model_name)
    
    # Save best model
    save_best_model(best_model, best_model_name, encoders, scaler, feature_names)
    
    print(f"\n{'='*60}")
    print("✓ Model training completed successfully!")
    print(f"✓ Best model: {best_model_name}")
    print(f"✓ Test accuracy: {test_acc:.4f}")
    print(f"✓ Model ready for deployment and predictions")
    print("="*60)

if __name__ == "__main__":
    main()
