"""
Script to download and organize DeepFashion dataset
"""

import os
import requests
import zipfile
import pandas as pd
from pathlib import Path
import yaml
from tqdm import tqdm

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def create_directories(config):
    """Create necessary directories for data storage"""
    paths = [
        config['dataset']['raw_data_path'],
        config['dataset']['processed_data_path'],
        config['dataset']['external_data_path']
    ]
    
    for path in paths:
        Path(path).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {path}")

def download_file(url, filename, chunk_size=8192):
    """Download a file with progress bar"""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filename, 'wb') as file, tqdm(
            desc=filename,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"Downloaded: {filename}")
        return True
    except Exception as e:
        print(f"Error downloading {filename}: {e}")
        return False

def create_sample_data():
    """Create sample data structure for demonstration"""
    config = load_config()
    raw_path = Path(config['dataset']['raw_data_path'])
    
    # Create sample annotation files
    sample_categories = [
        'Blouse', 'Dress', 'Jeans', 'Jacket', 'Skirt', 'Shorts', 'T-shirt', 'Sweater',
        'Coat', 'Pants', 'Cardigan', 'Hoodie', 'Tank Top', 'Blazer', 'Jumpsuit'
    ]
    
    sample_attributes = [
        'color_red', 'color_blue', 'color_black', 'color_white', 'color_green',
        'pattern_solid', 'pattern_striped', 'pattern_floral', 'pattern_geometric',
        'sleeve_long', 'sleeve_short', 'sleeve_sleeveless',
        'neckline_round', 'neckline_v', 'neckline_collar',
        'length_short', 'length_medium', 'length_long',
        'fit_loose', 'fit_regular', 'fit_tight'
    ]
    
    # Create sample data
    import random
    random.seed(42)
    
    sample_data = []
    for i in range(1000):  # Create 1000 sample records
        record = {
            'image_id': f'img_{i:06d}.jpg',
            'category': random.choice(sample_categories),
            'category_id': sample_categories.index(random.choice(sample_categories)),
        }
        
        # Add random attributes
        for attr in sample_attributes:
            record[attr] = random.choice([0, 1])
        
        # Add bounding box coordinates
        record['bbox_x'] = random.randint(10, 50)
        record['bbox_y'] = random.randint(10, 50)
        record['bbox_width'] = random.randint(100, 200)
        record['bbox_height'] = random.randint(150, 300)
        
        sample_data.append(record)
    
    # Save sample data
    df = pd.DataFrame(sample_data)
    sample_file = raw_path / 'sample_annotations.csv'
    df.to_csv(sample_file, index=False)
    print(f"Created sample data: {sample_file}")
    
    # Create README for dataset
    readme_content = """# DeepFashion Dataset

## Mô tả
Đây là dataset mẫu cho dự án phân tích xu hướng thời trang.
Dataset thực tế DeepFashion cần được tải từ: http://mmlab.ie.cuhk.edu.hk/projects/DeepFashion.html

## Cấu trúc dữ liệu
- `sample_annotations.csv`: Chú thích mẫu cho các item thời trang
- Các cột chính:
  - `image_id`: ID của ảnh
  - `category`: Loại quần áo
  - `category_id`: ID số của loại quần áo
  - `color_*`: Thuộc tính màu sắc
  - `pattern_*`: Thuộc tính họa tiết
  - `sleeve_*`: Thuộc tính tay áo
  - `neckline_*`: Thuộc tính cổ áo
  - `length_*`: Thuộc tính độ dài
  - `fit_*`: Thuộc tính độ vừa vặn
  - `bbox_*`: Tọa độ bounding box

## Hướng dẫn sử dụng
1. Để sử dụng dataset thực tế, cần đăng ký và tải từ trang chủ DeepFashion
2. Giải nén và đặt vào thư mục `data/raw/`
3. Chạy script tiền xử lý để chuẩn bị dữ liệu
"""
    
    readme_file = raw_path / 'README.md'
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"Created README: {readme_file}")

def main():
    """Main function to download and organize dataset"""
    print("Setting up DeepFashion dataset...")
    
    # Load configuration
    config = load_config()
    
    # Create directories
    create_directories(config)
    
    # Note about dataset access
    print("\n" + "="*60)
    print("THÔNG BÁO QUAN TRỌNG VỀ DEEPFASHION DATASET")
    print("="*60)
    print("DeepFashion dataset yêu cầu đăng ký để tải xuống.")
    print("Vui lòng làm theo các bước sau:")
    print("1. Truy cập: http://mmlab.ie.cuhk.edu.hk/projects/DeepFashion.html")
    print("2. Tải và điền form đăng ký")
    print("3. Gửi email đến tác giả để nhận mật khẩu")
    print("4. Tải dataset và giải nén vào thư mục data/raw/")
    print("\nHiện tại, tôi sẽ tạo dữ liệu mẫu để demo:")
    print("="*60)
    
    # Create sample data for demonstration
    create_sample_data()
    
    print("\n✓ Dataset setup completed!")
    print("- Sample data created for demonstration")
    print("- Directory structure ready")
    print("- Ready for data preprocessing")

if __name__ == "__main__":
    main()
