# Configuration file for Fashion Trend Analysis Project

# Dataset Configuration
dataset:
  name: "DeepFashion"
  url: "http://mmlab.ie.cuhk.edu.hk/projects/DeepFashion.html"
  raw_data_path: "data/raw/"
  processed_data_path: "data/processed/"
  external_data_path: "data/external/"
  
  # Dataset specifications
  total_images: 800000
  categories: 50
  attributes: 1000
  image_format: "jpg"
  annotation_format: "csv"

# Data Processing
preprocessing:
  image_size: [224, 224]
  batch_size: 32
  validation_split: 0.2
  test_split: 0.1
  random_seed: 42
  
  # Image augmentation
  augmentation:
    rotation_range: 20
    width_shift_range: 0.1
    height_shift_range: 0.1
    horizontal_flip: true
    zoom_range: 0.1

# Model Configuration
models:
  # Classification model
  classification:
    model_type: "cnn"
    architecture: "resnet50"
    input_shape: [224, 224, 3]
    num_classes: 50
    learning_rate: 0.001
    epochs: 100
    early_stopping_patience: 10
    
  # Recommendation system
  recommendation:
    algorithm: "collaborative_filtering"
    n_recommendations: 10
    similarity_metric: "cosine"
    
  # Trend analysis
  trend_analysis:
    time_window: "monthly"
    trend_threshold: 0.1
    seasonal_decomposition: true

# Visualization
visualization:
  figure_size: [12, 8]
  color_palette: "viridis"
  dpi: 300
  save_format: "png"

# Dashboard Configuration
dashboard:
  title: "Fashion Trend Analysis Dashboard"
  port: 8501
  theme: "light"
  
  # Pages
  pages:
    - "Overview"
    - "Data Exploration"
    - "Model Performance"
    - "Trend Analysis"
    - "Recommendations"

# Paths
paths:
  models: "models/"
  reports: "reports/"
  figures: "reports/figures/"
  notebooks: "notebooks/"
  dashboard: "dashboard/"

# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/fashion_analysis.log"
