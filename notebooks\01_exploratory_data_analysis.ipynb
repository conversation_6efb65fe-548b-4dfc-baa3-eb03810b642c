{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON> tích <PERSON> li<PERSON> (EDA) - <PERSON>ời trang\n", "\n", "Notebook này thực hiện phân tích khám phá dữ liệu DeepFashion để hiểu về:\n", "- <PERSON><PERSON> b<PERSON> các lo<PERSON>i quần áo\n", "- <PERSON><PERSON><PERSON><PERSON> t<PERSON>h màu <PERSON>, h<PERSON><PERSON> ti<PERSON>t\n", "- Đặc điểm hình học của bounding boxes\n", "- <PERSON><PERSON><PERSON> quan hệ gi<PERSON><PERSON> các thu<PERSON> t<PERSON>h"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import yaml\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# <PERSON><PERSON><PERSON> hình hiển thị\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 12\n", "\n", "print(\"✓ Imported libraries successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load configuration\n", "with open('../config.yaml', 'r', encoding='utf-8') as file:\n", "    config = yaml.safe_load(file)\n", "\n", "print(\"Configuration loaded:\")\n", "print(f\"- Dataset: {config['dataset']['name']}\")\n", "print(f\"- Total images: {config['dataset']['total_images']:,}\")\n", "print(f\"- Categories: {config['dataset']['categories']}\")\n", "print(f\"- Attributes: {config['dataset']['attributes']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load raw data\n", "raw_data_path = Path('../data/raw')\n", "df = pd.read_csv(raw_data_path / 'sample_annotations.csv')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {len(df.columns)}\")\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic dataset information\n", "print(\"=== THÔNG TIN CƠ BẢN VỀ DATASET ===\")\n", "print(f\"Số lượng mẫu: {len(df):,}\")\n", "print(f\"<PERSON><PERSON> lượng thuộc tính: {len(df.columns)}\")\n", "print(f\"Dung lượng bộ nhớ: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "print(\"\\n=== KIỂU DỮ LIỆU ===\")\n", "print(df.dtypes.value_counts())\n", "\n", "print(\"\\n=== GIÁ TRỊ THIẾU ===\")\n", "missing_values = df.isnull().sum()\n", "if missing_values.sum() == 0:\n", "    print(\"✓ Không có giá trị thiếu\")\n", "else:\n", "    print(missing_values[missing_values > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> t<PERSON>ch <PERSON> bố Loại Quần áo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> b<PERSON> các lo<PERSON>i quần áo\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Bar chart\n", "category_counts = df['category'].value_counts()\n", "category_counts.plot(kind='bar', ax=ax1, color='skyblue')\n", "ax1.set_title('<PERSON><PERSON> bố Lo<PERSON> Quần áo', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Loại quần áo')\n", "ax1.set_ylabel('Số lượng')\n", "ax1.tick_params(axis='x', rotation=45)\n", "\n", "# Pie chart\n", "ax2.pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%', startangle=90)\n", "ax2.set_title('Tỷ lệ <PERSON> Q<PERSON>ần áo', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Top 5 loại quần áo phổ biến nhất:\")\n", "print(category_counts.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> t<PERSON><PERSON> t<PERSON>c"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> tích thu<PERSON>c t<PERSON>h màu sắc\n", "color_cols = [col for col in df.columns if col.startswith('color_')]\n", "color_data = df[color_cols].sum().sort_values(ascending=False)\n", "\n", "plt.figure(figsize=(12, 6))\n", "color_data.plot(kind='bar', color=['red', 'blue', 'black', 'lightgray', 'green'])\n", "plt.title('<PERSON><PERSON> b<PERSON> t<PERSON> sắ<PERSON>', fontsize=14, fontweight='bold')\n", "plt.xlabel('<PERSON><PERSON><PERSON> sắ<PERSON>')\n", "plt.ylabel('Số lượng item')\n", "plt.xticks(rotation=45)\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Thống kê màu sắc:\")\n", "for color, count in color_data.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"{color}: {count} items ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON> t<PERSON><PERSON> t<PERSON> t<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> tích thu<PERSON><PERSON> t<PERSON>h họa tiết\n", "pattern_cols = [col for col in df.columns if col.startswith('pattern_')]\n", "pattern_data = df[pattern_cols].sum().sort_values(ascending=False)\n", "\n", "plt.figure(figsize=(10, 6))\n", "pattern_data.plot(kind='bar', color='lightcoral')\n", "plt.title('<PERSON><PERSON> b<PERSON> t<PERSON> tiế<PERSON>', fontsize=14, fontweight='bold')\n", "plt.xlabel('<PERSON><PERSON><PERSON> tiết')\n", "plt.ylabel('Số lượng item')\n", "plt.xticks(rotation=45)\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Th<PERSON>ng kê họa tiết:\")\n", "for pattern, count in pattern_data.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"{pattern}: {count} items ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON> t<PERSON> Bounding Box"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> tích kích thước bounding box\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Width distribution\n", "axes[0,0].hist(df['bbox_width'], bins=30, alpha=0.7, color='skyblue')\n", "axes[0,0].set_title('Phân bố Chiều rộng Bounding Box')\n", "axes[0,0].set_xlabel('Chiều rộng (pixels)')\n", "axes[0,0].set_ylabel('Tần suất')\n", "\n", "# Height distribution\n", "axes[0,1].hist(df['bbox_height'], bins=30, alpha=0.7, color='lightcoral')\n", "axes[0,1].set_title('Phân bố <PERSON>u cao Bounding Box')\n", "axes[0,1].set_xlabel('Chiều cao (pixels)')\n", "axes[0,1].set_ylabel('Tần suất')\n", "\n", "# Aspect ratio\n", "df['aspect_ratio'] = df['bbox_width'] / df['bbox_height']\n", "axes[1,0].hist(df['aspect_ratio'], bins=30, alpha=0.7, color='lightgreen')\n", "axes[1,0].set_title('<PERSON><PERSON> bố Tỷ lệ <PERSON>hung hình')\n", "axes[1,0].set_xlabel('Tỷ lệ (width/height)')\n", "axes[1,0].set_ylabel('Tần suất')\n", "\n", "# Area\n", "df['bbox_area'] = df['bbox_width'] * df['bbox_height']\n", "axes[1,1].hist(df['bbox_area'], bins=30, alpha=0.7, color='gold')\n", "axes[1,1].set_title('Phân bố Diện tích Bounding Box')\n", "axes[1,1].set_xlabel('<PERSON><PERSON>n tích (pixels²)')\n", "axes[1,1].set_ylabel('Tần suất')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Thống kê Bounding Box:\")\n", "print(f\"Chiều rộng trung bình: {df['bbox_width'].mean():.1f} ± {df['bbox_width'].std():.1f}\")\n", "print(f\"<PERSON>ều cao trung bình: {df['bbox_height'].mean():.1f} ± {df['bbox_height'].std():.1f}\")\n", "print(f\"Tỷ lệ khung hình trung bình: {df['aspect_ratio'].mean():.2f} ± {df['aspect_ratio'].std():.2f}\")\n", "print(f\"<PERSON>ện tích trung bình: {df['bbox_area'].mean():.0f} ± {df['bbox_area'].std():.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON> tích <PERSON><PERSON> quan hệ giữa <PERSON> quần áo và <PERSON>hu<PERSON> t<PERSON>h"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Heatmap mối quan hệ giữa category và color\n", "color_by_category = pd.crosstab(df['category'], [df[col] for col in color_cols])\n", "\n", "plt.figure(figsize=(12, 8))\n", "sns.heatmap(color_by_category, annot=True, fmt='d', cmap='YlOrRd')\n", "plt.title('<PERSON><PERSON><PERSON> quan hệ giữa Loại quần áo và <PERSON>à<PERSON> sắc', fontsize=14, fontweight='bold')\n", "plt.xlabel('<PERSON><PERSON><PERSON> sắ<PERSON>')\n", "plt.ylabel('Loại quần áo')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Phân tích correlation matrix\n", "# <PERSON><PERSON><PERSON> c<PERSON>c thu<PERSON>c t<PERSON> số\n", "numeric_cols = ['bbox_x', 'bbox_y', 'bbox_width', 'bbox_height', 'aspect_ratio', 'bbox_area']\n", "correlation_matrix = df[numeric_cols].corr()\n", "\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('<PERSON> trận <PERSON> quan - <PERSON><PERSON><PERSON><PERSON> t<PERSON>h <PERSON> họ<PERSON>', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Các cặp thuộc tính có tương quan cao (|r| > 0.5):\")\n", "for i in range(len(correlation_matrix.columns)):\n", "    for j in range(i+1, len(correlation_matrix.columns)):\n", "        corr_val = correlation_matrix.iloc[i, j]\n", "        if abs(corr_val) > 0.5:\n", "            print(f\"{correlation_matrix.columns[i]} - {correlation_matrix.columns[j]}: {corr_val:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON> t<PERSON><PERSON> và Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tạo feature t<PERSON><PERSON> h<PERSON><PERSON>\n", "attribute_cols = [col for col in df.columns if any(\n", "    col.startswith(prefix) for prefix in ['color_', 'pattern_', 'sleeve_', 'neckline_', 'length_', 'fit_']\n", ")]\n", "\n", "df['total_attributes'] = df[attribute_cols].sum(axis=1)\n", "df['color_count'] = df[color_cols].sum(axis=1)\n", "df['pattern_count'] = df[pattern_cols].sum(axis=1)\n", "\n", "# <PERSON><PERSON> tích theo category\n", "category_analysis = df.groupby('category').agg({\n", "    'total_attributes': 'mean',\n", "    'color_count': 'mean',\n", "    'pattern_count': 'mean',\n", "    'bbox_area': 'mean',\n", "    'aspect_ratio': 'mean'\n", "}).round(2)\n", "\n", "print(\"=== PHÂN TÍCH THEO LOẠI QUẦN ÁO ===\")\n", "print(category_analysis)\n", "\n", "# Visualize\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "category_analysis['total_attributes'].plot(kind='bar', ax=axes[0,0], color='skyblue')\n", "axes[0,0].set_title('Trung bình <PERSON> t<PERSON>h the<PERSON>')\n", "axes[0,0].tick_params(axis='x', rotation=45)\n", "\n", "category_analysis['bbox_area'].plot(kind='bar', ax=axes[0,1], color='lightcoral')\n", "axes[0,1].set_title('Trung bình <PERSON> tích theo <PERSON>')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "category_analysis['color_count'].plot(kind='bar', ax=axes[1,0], color='lightgreen')\n", "axes[1,0].set_title('Trung bình Số M<PERSON> the<PERSON>')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "category_analysis['aspect_ratio'].plot(kind='bar', ax=axes[1,1], color='gold')\n", "axes[1,1].set_title('Trung bình Tỷ lệ <PERSON>hung hình theo <PERSON>')\n", "axes[1,1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> và Insights\n", "\n", "### <PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hi<PERSON> ch<PERSON>:\n", "\n", "1. **<PERSON><PERSON> bố Loại quần áo**: Dataset có sự phân bố tương đối đều giữa các loại quần áo\n", "\n", "2. **<PERSON><PERSON><PERSON><PERSON> t<PERSON>h <PERSON> sắ<PERSON>**: <PERSON><PERSON><PERSON> màu c<PERSON> bản nh<PERSON> đen, <PERSON><PERSON><PERSON><PERSON>, xan<PERSON> chi<PERSON>m <PERSON>u thế\n", "\n", "3. **H<PERSON><PERSON> tiết**: <PERSON><PERSON><PERSON> tiết đơn giản (solid) phổ biến nhất\n", "\n", "4. **Đặc điểm <PERSON> h<PERSON>**: Bounding boxes có kích thước và tỷ lệ đa dạng\n", "\n", "5. **<PERSON><PERSON><PERSON> quan hệ**: <PERSON><PERSON> sự tương quan giữa loại quần áo và các thuộc t<PERSON>h\n", "\n", "### Ứng dụng cho Machine Learning:\n", "\n", "- <PERSON><PERSON> liệu đã đư<PERSON><PERSON> làm sạch và chuẩn hóa\n", "- Các feature đ<PERSON> đ<PERSON> engineering phù hợp\n", "- Sẵn sàng cho việc xây dựng mô hình phân loại và dự đoán xu hướng"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}