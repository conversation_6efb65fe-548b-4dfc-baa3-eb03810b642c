"""
Fashion Trend Analysis Dashboard
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from models.recommendation_system import FashionRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Fashion Trend Analysis",
    page_icon="👗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #FF6B6B;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #FF6B6B;
    }
    .recommendation-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and cache data"""
    try:
        # Load raw data
        raw_path = Path(__file__).parent.parent / 'data' / 'raw'
        df = pd.read_csv(raw_path / 'sample_annotations.csv')
        
        # Add derived features
        df['aspect_ratio'] = df['bbox_width'] / df['bbox_height']
        df['bbox_area'] = df['bbox_width'] * df['bbox_height']
        
        # Add color and pattern counts
        color_cols = [col for col in df.columns if col.startswith('color_')]
        pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
        df['color_count'] = df[color_cols].sum(axis=1)
        df['pattern_count'] = df[pattern_cols].sum(axis=1)
        
        return df
    except Exception as e:
        st.error(f"Error loading data: {e}")
        return None

def create_category_chart(df):
    """Create category distribution chart"""
    category_counts = df['category'].value_counts()
    
    fig = px.bar(
        x=category_counts.index,
        y=category_counts.values,
        title="Phân bố Loại Quần áo",
        labels={'x': 'Loại quần áo', 'y': 'Số lượng'},
        color=category_counts.values,
        color_continuous_scale='viridis'
    )
    
    fig.update_layout(
        xaxis_tickangle=-45,
        height=500,
        showlegend=False
    )
    
    return fig

def create_color_analysis(df):
    """Create color analysis charts"""
    color_cols = [col for col in df.columns if col.startswith('color_')]
    color_data = df[color_cols].sum().sort_values(ascending=False)
    
    # Pie chart
    fig = px.pie(
        values=color_data.values,
        names=[col.replace('color_', '').title() for col in color_data.index],
        title="Phân bố Màu sắc",
        color_discrete_sequence=px.colors.qualitative.Set3
    )
    
    return fig

def create_trend_analysis(df):
    """Create trend analysis chart"""
    # Calculate trend scores
    trend_scores = []
    for idx, item in df.iterrows():
        score = 0
        
        # Category popularity (inverse)
        category_count = len(df[df['category'] == item['category']])
        category_score = 1.0 / (category_count / len(df))
        score += category_score * 0.4
        
        # Color diversity
        color_count = item['color_count']
        color_score = min(color_count / 3.0, 1.0)
        score += color_score * 0.3
        
        # Size factor
        area_norm = item['bbox_area'] / df['bbox_area'].max()
        size_score = 1.0 - abs(area_norm - 0.5) * 2
        score += size_score * 0.3
        
        trend_scores.append(score)
    
    df['trend_score'] = trend_scores
    
    # Top trending categories
    trending_categories = df.groupby('category')['trend_score'].mean().sort_values(ascending=False).head(10)
    
    fig = px.bar(
        x=trending_categories.values,
        y=trending_categories.index,
        orientation='h',
        title="Top 10 Loại Quần áo Xu hướng",
        labels={'x': 'Điểm xu hướng', 'y': 'Loại quần áo'},
        color=trending_categories.values,
        color_continuous_scale='plasma'
    )
    
    fig.update_layout(height=500)
    
    return fig

def create_size_analysis(df):
    """Create size analysis charts"""
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Chiều rộng', 'Chiều cao', 'Tỷ lệ khung hình', 'Diện tích'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # Width histogram
    fig.add_trace(
        go.Histogram(x=df['bbox_width'], name='Width', nbinsx=30),
        row=1, col=1
    )
    
    # Height histogram
    fig.add_trace(
        go.Histogram(x=df['bbox_height'], name='Height', nbinsx=30),
        row=1, col=2
    )
    
    # Aspect ratio histogram
    fig.add_trace(
        go.Histogram(x=df['aspect_ratio'], name='Aspect Ratio', nbinsx=30),
        row=2, col=1
    )
    
    # Area histogram
    fig.add_trace(
        go.Histogram(x=df['bbox_area'], name='Area', nbinsx=30),
        row=2, col=2
    )
    
    fig.update_layout(
        height=600,
        title_text="Phân tích Kích thước Bounding Box",
        showlegend=False
    )
    
    return fig

def main():
    """Main dashboard function"""
    
    # Header
    st.markdown('<h1 class="main-header">👗 Fashion Trend Analysis Dashboard</h1>', unsafe_allow_html=True)
    
    # Load data
    df = load_data()
    if df is None:
        st.error("Không thể tải dữ liệu. Vui lòng kiểm tra lại.")
        return
    
    # Sidebar
    st.sidebar.title("🎛️ Điều khiển")
    
    # Page selection
    page = st.sidebar.selectbox(
        "Chọn trang",
        ["📊 Tổng quan", "🎨 Phân tích màu sắc", "📈 Xu hướng", "🔍 Gợi ý thời trang", "📏 Phân tích kích thước"]
    )
    
    # Main content based on page selection
    if page == "📊 Tổng quan":
        show_overview(df)
    elif page == "🎨 Phân tích màu sắc":
        show_color_analysis(df)
    elif page == "📈 Xu hướng":
        show_trend_analysis(df)
    elif page == "🔍 Gợi ý thời trang":
        show_recommendations(df)
    elif page == "📏 Phân tích kích thước":
        show_size_analysis(df)

def show_overview(df):
    """Show overview page"""
    st.header("📊 Tổng quan Dataset")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(
            f'<div class="metric-card"><h3>{len(df):,}</h3><p>Tổng số items</p></div>',
            unsafe_allow_html=True
        )
    
    with col2:
        st.markdown(
            f'<div class="metric-card"><h3>{df["category"].nunique()}</h3><p>Loại quần áo</p></div>',
            unsafe_allow_html=True
        )
    
    with col3:
        avg_colors = df['color_count'].mean()
        st.markdown(
            f'<div class="metric-card"><h3>{avg_colors:.1f}</h3><p>Trung bình màu/item</p></div>',
            unsafe_allow_html=True
        )
    
    with col4:
        avg_area = df['bbox_area'].mean()
        st.markdown(
            f'<div class="metric-card"><h3>{avg_area:,.0f}</h3><p>Diện tích TB (px²)</p></div>',
            unsafe_allow_html=True
        )
    
    # Category distribution
    st.subheader("Phân bố Loại Quần áo")
    fig_category = create_category_chart(df)
    st.plotly_chart(fig_category, use_container_width=True)
    
    # Top categories table
    st.subheader("Top 10 Loại Quần áo Phổ biến")
    top_categories = df['category'].value_counts().head(10)
    
    category_df = pd.DataFrame({
        'Loại quần áo': top_categories.index,
        'Số lượng': top_categories.values,
        'Tỷ lệ (%)': (top_categories.values / len(df) * 100).round(1)
    })
    
    st.dataframe(category_df, use_container_width=True)

def show_color_analysis(df):
    """Show color analysis page"""
    st.header("🎨 Phân tích Màu sắc")
    
    # Color distribution pie chart
    fig_color = create_color_analysis(df)
    st.plotly_chart(fig_color, use_container_width=True)
    
    # Color by category heatmap
    st.subheader("Màu sắc theo Loại Quần áo")
    
    color_cols = [col for col in df.columns if col.startswith('color_')]
    color_by_category = pd.crosstab(df['category'], [df[col] for col in color_cols])
    
    fig_heatmap = px.imshow(
        color_by_category.values,
        x=[col.replace('color_', '').title() for col in color_cols],
        y=color_by_category.index,
        title="Heatmap: Màu sắc theo Loại Quần áo",
        color_continuous_scale='viridis'
    )
    
    st.plotly_chart(fig_heatmap, use_container_width=True)

def show_trend_analysis(df):
    """Show trend analysis page"""
    st.header("📈 Phân tích Xu hướng")
    
    # Trending categories
    fig_trend = create_trend_analysis(df)
    st.plotly_chart(fig_trend, use_container_width=True)
    
    # Trend insights
    st.subheader("💡 Insights Xu hướng")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Xu hướng Màu sắc:**")
        color_cols = [col for col in df.columns if col.startswith('color_')]
        color_trends = df[color_cols].sum().sort_values(ascending=False)
        
        for i, (color, count) in enumerate(color_trends.head(3).items(), 1):
            color_name = color.replace('color_', '').title()
            percentage = (count / len(df)) * 100
            st.write(f"{i}. {color_name}: {percentage:.1f}%")
    
    with col2:
        st.write("**Xu hướng Họa tiết:**")
        pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
        pattern_trends = df[pattern_cols].sum().sort_values(ascending=False)
        
        for i, (pattern, count) in enumerate(pattern_trends.head(3).items(), 1):
            pattern_name = pattern.replace('pattern_', '').title()
            percentage = (count / len(df)) * 100
            st.write(f"{i}. {pattern_name}: {percentage:.1f}%")

def show_recommendations(df):
    """Show recommendations page"""
    st.header("🔍 Hệ thống Gợi ý Thời trang")
    
    # Item selection
    st.subheader("Chọn Item để nhận Gợi ý")
    
    col1, col2 = st.columns(2)
    
    with col1:
        selected_category = st.selectbox(
            "Chọn loại quần áo:",
            options=["Tất cả"] + list(df['category'].unique())
        )
    
    with col2:
        if selected_category != "Tất cả":
            filtered_df = df[df['category'] == selected_category]
        else:
            filtered_df = df
        
        selected_item_id = st.selectbox(
            "Chọn item ID:",
            options=filtered_df.index.tolist()
        )
    
    if st.button("🎯 Tạo Gợi ý"):
        try:
            # Initialize recommendation system
            rec_system = FashionRecommendationSystem()
            rec_system.load_data()
            rec_system.prepare_features()
            rec_system.build_similarity_matrix()
            rec_system.perform_clustering()
            
            # Get recommendations
            similar_items = rec_system.get_similar_items(selected_item_id, n_recommendations=5)
            
            # Display original item
            original_item = df.iloc[selected_item_id]
            st.subheader(f"📦 Item gốc: {original_item['category']} (ID: {selected_item_id})")
            
            # Display recommendations
            st.subheader("✨ Gợi ý tương tự:")
            
            for i, item in enumerate(similar_items, 1):
                with st.container():
                    st.markdown(
                        f'''
                        <div class="recommendation-card">
                            <h4>{i}. {item['category']} (ID: {item['item_id']})</h4>
                            <p><strong>Độ tương tự:</strong> {item['similarity_score']:.3f}</p>
                            <p><strong>Cluster:</strong> {item['cluster']}</p>
                            <p><strong>Màu sắc:</strong> {", ".join(item['features']['colors']) if item['features']['colors'] else "Không có"}</p>
                            <p><strong>Họa tiết:</strong> {", ".join(item['features']['patterns']) if item['features']['patterns'] else "Không có"}</p>
                        </div>
                        ''',
                        unsafe_allow_html=True
                    )
            
        except Exception as e:
            st.error(f"Lỗi khi tạo gợi ý: {e}")

def show_size_analysis(df):
    """Show size analysis page"""
    st.header("📏 Phân tích Kích thước")
    
    # Size distribution charts
    fig_size = create_size_analysis(df)
    st.plotly_chart(fig_size, use_container_width=True)
    
    # Size statistics
    st.subheader("📊 Thống kê Kích thước")
    
    size_stats = pd.DataFrame({
        'Metric': ['Chiều rộng', 'Chiều cao', 'Tỷ lệ khung hình', 'Diện tích'],
        'Trung bình': [
            df['bbox_width'].mean(),
            df['bbox_height'].mean(),
            df['aspect_ratio'].mean(),
            df['bbox_area'].mean()
        ],
        'Độ lệch chuẩn': [
            df['bbox_width'].std(),
            df['bbox_height'].std(),
            df['aspect_ratio'].std(),
            df['bbox_area'].std()
        ],
        'Min': [
            df['bbox_width'].min(),
            df['bbox_height'].min(),
            df['aspect_ratio'].min(),
            df['bbox_area'].min()
        ],
        'Max': [
            df['bbox_width'].max(),
            df['bbox_height'].max(),
            df['aspect_ratio'].max(),
            df['bbox_area'].max()
        ]
    })
    
    # Format numbers
    for col in ['Trung bình', 'Độ lệch chuẩn', 'Min', 'Max']:
        size_stats[col] = size_stats[col].round(2)
    
    st.dataframe(size_stats, use_container_width=True)

if __name__ == "__main__":
    main()
