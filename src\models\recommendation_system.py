"""
Fashion Recommendation System
"""

import pandas as pd
import numpy as np
from pathlib import Path
import yaml
import joblib
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class FashionRecommendationSystem:
    """Fashion item recommendation system"""
    
    def __init__(self):
        """Initialize the recommendation system"""
        self.data = None
        self.features = None
        self.similarity_matrix = None
        self.clusters = None
        self.pca = None
        self.encoders = None
        self.item_features = None
        
    def load_data(self):
        """Load and prepare data for recommendations"""
        print("Loading data for recommendation system...")
        
        # Load raw data
        raw_path = Path('data/raw')
        self.data = pd.read_csv(raw_path / 'sample_annotations.csv')
        
        # Load encoders
        processed_path = Path('data/processed')
        self.encoders = joblib.load(processed_path / 'encoders.pkl')
        
        print(f"Loaded {len(self.data)} fashion items")
        
    def prepare_features(self):
        """Prepare features for similarity calculation"""
        print("Preparing features for recommendations...")
        
        # Select feature columns
        feature_cols = []
        
        # Color features
        color_cols = [col for col in self.data.columns if col.startswith('color_')]
        feature_cols.extend(color_cols)
        
        # Pattern features
        pattern_cols = [col for col in self.data.columns if col.startswith('pattern_')]
        feature_cols.extend(pattern_cols)
        
        # Style features
        style_prefixes = ['sleeve_', 'neckline_', 'length_', 'fit_']
        for prefix in style_prefixes:
            style_cols = [col for col in self.data.columns if col.startswith(prefix)]
            feature_cols.extend(style_cols)
        
        # Geometric features (normalized)
        self.data['aspect_ratio'] = self.data['bbox_width'] / self.data['bbox_height']
        self.data['bbox_area'] = self.data['bbox_width'] * self.data['bbox_height']
        
        # Normalize geometric features
        geometric_cols = ['bbox_width', 'bbox_height', 'aspect_ratio', 'bbox_area']
        for col in geometric_cols:
            self.data[f'{col}_norm'] = (self.data[col] - self.data[col].min()) / (self.data[col].max() - self.data[col].min())
            feature_cols.append(f'{col}_norm')
        
        # Create feature matrix
        self.features = self.data[feature_cols].values
        self.item_features = feature_cols
        
        print(f"Prepared {len(feature_cols)} features for recommendations")
        
    def build_similarity_matrix(self):
        """Build item-item similarity matrix"""
        print("Building similarity matrix...")
        
        # Calculate cosine similarity
        self.similarity_matrix = cosine_similarity(self.features)
        
        print(f"Built similarity matrix: {self.similarity_matrix.shape}")
        
    def perform_clustering(self, n_clusters=5):
        """Perform clustering to group similar items"""
        print(f"Performing clustering with {n_clusters} clusters...")
        
        # Apply PCA for dimensionality reduction
        self.pca = PCA(n_components=min(10, self.features.shape[1]))
        features_pca = self.pca.fit_transform(self.features)
        
        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        self.clusters = kmeans.fit_predict(features_pca)
        
        # Add cluster information to data
        self.data['cluster'] = self.clusters
        
        print("Clustering completed")
        
        # Analyze clusters
        self.analyze_clusters()
        
    def analyze_clusters(self):
        """Analyze the characteristics of each cluster"""
        print("\nCluster Analysis:")
        print("="*40)
        
        for cluster_id in range(max(self.clusters) + 1):
            cluster_items = self.data[self.data['cluster'] == cluster_id]
            
            print(f"\nCluster {cluster_id} ({len(cluster_items)} items):")
            
            # Most common category
            top_category = cluster_items['category'].mode()
            if len(top_category) > 0:
                print(f"  Top category: {top_category.iloc[0]}")
            
            # Most common colors
            color_cols = [col for col in self.data.columns if col.startswith('color_')]
            color_sums = cluster_items[color_cols].sum()
            top_color = color_sums.idxmax().replace('color_', '') if len(color_sums) > 0 else 'None'
            print(f"  Top color: {top_color}")
            
            # Most common patterns
            pattern_cols = [col for col in self.data.columns if col.startswith('pattern_')]
            pattern_sums = cluster_items[pattern_cols].sum()
            top_pattern = pattern_sums.idxmax().replace('pattern_', '') if len(pattern_sums) > 0 else 'None'
            print(f"  Top pattern: {top_pattern}")
            
            # Average size
            avg_area = cluster_items['bbox_area'].mean()
            print(f"  Avg area: {avg_area:.0f}")
    
    def get_similar_items(self, item_id, n_recommendations=5):
        """Get similar items based on item ID"""
        if item_id >= len(self.data):
            raise ValueError(f"Item ID {item_id} not found")
        
        # Get similarity scores for the item
        similarities = self.similarity_matrix[item_id]
        
        # Get top similar items (excluding the item itself)
        similar_indices = np.argsort(similarities)[::-1][1:n_recommendations+1]
        
        recommendations = []
        for idx in similar_indices:
            item = self.data.iloc[idx]
            recommendations.append({
                'item_id': idx,
                'category': item['category'],
                'similarity_score': similarities[idx],
                'cluster': item['cluster'],
                'features': {
                    'colors': [col.replace('color_', '') for col in self.data.columns 
                              if col.startswith('color_') and item[col] == 1],
                    'patterns': [col.replace('pattern_', '') for col in self.data.columns 
                                if col.startswith('pattern_') and item[col] == 1]
                }
            })
        
        return recommendations
    
    def get_cluster_recommendations(self, item_id, n_recommendations=5):
        """Get recommendations from the same cluster"""
        if item_id >= len(self.data):
            raise ValueError(f"Item ID {item_id} not found")
        
        item_cluster = self.data.iloc[item_id]['cluster']
        cluster_items = self.data[self.data['cluster'] == item_cluster]
        
        # Exclude the original item
        cluster_items = cluster_items[cluster_items.index != item_id]
        
        # Sample random items from the cluster
        n_recommendations = min(n_recommendations, len(cluster_items))
        if n_recommendations > 0:
            sampled_items = cluster_items.sample(n_recommendations)
            
            recommendations = []
            for idx, item in sampled_items.iterrows():
                recommendations.append({
                    'item_id': idx,
                    'category': item['category'],
                    'cluster': item['cluster'],
                    'features': {
                        'colors': [col.replace('color_', '') for col in self.data.columns 
                                  if col.startswith('color_') and item[col] == 1],
                        'patterns': [col.replace('pattern_', '') for col in self.data.columns 
                                    if col.startswith('pattern_') and item[col] == 1]
                    }
                })
            
            return recommendations
        
        return []
    
    def get_category_recommendations(self, category, n_recommendations=5):
        """Get popular items from a specific category"""
        category_items = self.data[self.data['category'] == category]
        
        if len(category_items) == 0:
            return []
        
        # Sample random items from the category
        n_recommendations = min(n_recommendations, len(category_items))
        sampled_items = category_items.sample(n_recommendations)
        
        recommendations = []
        for idx, item in sampled_items.iterrows():
            recommendations.append({
                'item_id': idx,
                'category': item['category'],
                'cluster': item['cluster'],
                'features': {
                    'colors': [col.replace('color_', '') for col in self.data.columns 
                              if col.startswith('color_') and item[col] == 1],
                    'patterns': [col.replace('pattern_', '') for col in self.data.columns 
                                if col.startswith('pattern_') and item[col] == 1]
                }
            })
        
        return recommendations
    
    def get_trend_recommendations(self, n_recommendations=10):
        """Get trending items based on popularity and diversity"""
        print("Generating trend recommendations...")
        
        # Calculate trend scores based on various factors
        trend_scores = []
        
        for idx, item in self.data.iterrows():
            score = 0
            
            # Category popularity (inverse - less common = more trendy)
            category_count = len(self.data[self.data['category'] == item['category']])
            category_score = 1.0 / (category_count / len(self.data))
            score += category_score * 0.3
            
            # Color diversity
            color_cols = [col for col in self.data.columns if col.startswith('color_')]
            color_count = sum(item[col] for col in color_cols)
            color_score = min(color_count / 3.0, 1.0)  # Normalize to max 1.0
            score += color_score * 0.2
            
            # Pattern uniqueness
            pattern_cols = [col for col in self.data.columns if col.startswith('pattern_')]
            pattern_count = sum(item[col] for col in pattern_cols)
            pattern_score = min(pattern_count / 2.0, 1.0)
            score += pattern_score * 0.2
            
            # Size factor (medium sizes are more trendy)
            area_norm = item['bbox_area'] / self.data['bbox_area'].max()
            size_score = 1.0 - abs(area_norm - 0.5) * 2  # Peak at 0.5
            score += size_score * 0.3
            
            trend_scores.append(score)
        
        # Get top trending items
        self.data['trend_score'] = trend_scores
        trending_items = self.data.nlargest(n_recommendations, 'trend_score')
        
        recommendations = []
        for idx, item in trending_items.iterrows():
            recommendations.append({
                'item_id': idx,
                'category': item['category'],
                'trend_score': item['trend_score'],
                'cluster': item['cluster'],
                'features': {
                    'colors': [col.replace('color_', '') for col in self.data.columns 
                              if col.startswith('color_') and item[col] == 1],
                    'patterns': [col.replace('pattern_', '') for col in self.data.columns 
                                if col.startswith('pattern_') and item[col] == 1]
                }
            })
        
        return recommendations

def demo_recommendation_system():
    """Demonstrate the recommendation system"""
    print("Fashion Recommendation System Demo")
    print("="*50)
    
    # Initialize system
    rec_system = FashionRecommendationSystem()
    
    # Load and prepare data
    rec_system.load_data()
    rec_system.prepare_features()
    rec_system.build_similarity_matrix()
    rec_system.perform_clustering()
    
    # Demo 1: Similar items
    print(f"\n{'='*50}")
    print("DEMO 1: SIMILAR ITEMS")
    print("="*50)
    
    item_id = 0
    original_item = rec_system.data.iloc[item_id]
    print(f"Original item: {original_item['category']} (ID: {item_id})")
    
    similar_items = rec_system.get_similar_items(item_id, n_recommendations=3)
    print(f"\nTop 3 similar items:")
    for i, item in enumerate(similar_items, 1):
        print(f"  {i}. {item['category']} (ID: {item['item_id']}, "
              f"Similarity: {item['similarity_score']:.3f})")
    
    # Demo 2: Cluster recommendations
    print(f"\n{'='*50}")
    print("DEMO 2: CLUSTER RECOMMENDATIONS")
    print("="*50)
    
    cluster_recs = rec_system.get_cluster_recommendations(item_id, n_recommendations=3)
    print(f"Items from same cluster as item {item_id}:")
    for i, item in enumerate(cluster_recs, 1):
        print(f"  {i}. {item['category']} (ID: {item['item_id']}, "
              f"Cluster: {item['cluster']})")
    
    # Demo 3: Category recommendations
    print(f"\n{'='*50}")
    print("DEMO 3: CATEGORY RECOMMENDATIONS")
    print("="*50)
    
    category = "Dress"
    category_recs = rec_system.get_category_recommendations(category, n_recommendations=3)
    print(f"Popular {category} items:")
    for i, item in enumerate(category_recs, 1):
        print(f"  {i}. {item['category']} (ID: {item['item_id']})")
    
    # Demo 4: Trend recommendations
    print(f"\n{'='*50}")
    print("DEMO 4: TREND RECOMMENDATIONS")
    print("="*50)
    
    trend_recs = rec_system.get_trend_recommendations(n_recommendations=5)
    print("Top 5 trending items:")
    for i, item in enumerate(trend_recs, 1):
        print(f"  {i}. {item['category']} (ID: {item['item_id']}, "
              f"Trend Score: {item['trend_score']:.3f})")
    
    print(f"\n{'='*50}")
    print("✓ Recommendation system demo completed!")
    print("✓ System ready for generating personalized recommendations")
    print("="*50)

if __name__ == "__main__":
    demo_recommendation_system()
