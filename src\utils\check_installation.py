"""
<PERSON><PERSON><PERSON> to check if all required libraries are properly installed
"""

import sys
import importlib

def check_package(package_name, import_name=None):
    """Check if a package is installed and can be imported"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError:
        print(f"✗ {package_name}: Not installed")
        return False

def main():
    """Check all required packages"""
    print("Checking Python environment...")
    print(f"Python version: {sys.version}")
    print("\nChecking required packages:")
    
    packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scikit-learn', 'sklearn'),
        ('jupyter', 'jupyter'),
        ('notebook', 'notebook'),
    ]
    
    all_installed = True
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_installed = False
    
    if all_installed:
        print("\n✓ All basic packages are installed successfully!")
        print("Environment is ready for fashion trend analysis.")
    else:
        print("\n✗ Some packages are missing. Please install them.")
        return False
    
    return True

if __name__ == "__main__":
    main()
