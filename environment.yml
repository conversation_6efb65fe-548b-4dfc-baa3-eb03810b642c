name: fashion-trend-analysis
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  - python=3.9
  - pip
  - jupyter
  - notebook
  - pandas
  - numpy
  - scipy
  - scikit-learn
  - matplotlib
  - seaborn
  - plotly
  - opencv
  - pillow
  - requests
  - tqdm
  - pyyaml
  - pip:
    - streamlit
    - dash
    - dash-bootstrap-components
    - tensorflow
    - torch
    - torchvision
    - xgboost
    - lightgbm
    - albumentations
    - scikit-image
    - beautifulsoup4
    - python-dotenv
    - click
    - imageio
    - joblib
    - statsmodels
