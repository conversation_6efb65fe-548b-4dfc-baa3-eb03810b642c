"""
Data preprocessing module for DeepFashion dataset
"""

import pandas as pd
import numpy as np
from pathlib import Path
import yaml
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
import joblib

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def load_raw_data(config):
    """Load raw data from CSV files"""
    raw_path = Path(config['dataset']['raw_data_path'])
    
    # Load sample annotations
    annotations_file = raw_path / 'sample_annotations.csv'
    if annotations_file.exists():
        df = pd.read_csv(annotations_file)
        print(f"Loaded {len(df)} records from {annotations_file}")
        return df
    else:
        print(f"File not found: {annotations_file}")
        return None

def clean_data(df):
    """Clean and validate data"""
    print("Cleaning data...")
    
    # Remove duplicates
    initial_count = len(df)
    df = df.drop_duplicates()
    print(f"Removed {initial_count - len(df)} duplicate records")
    
    # Handle missing values
    missing_counts = df.isnull().sum()
    if missing_counts.sum() > 0:
        print("Missing values found:")
        print(missing_counts[missing_counts > 0])
        df = df.fillna(0)  # Fill with 0 for binary attributes
    
    # Validate bounding boxes
    df = df[
        (df['bbox_width'] > 0) & 
        (df['bbox_height'] > 0) &
        (df['bbox_x'] >= 0) &
        (df['bbox_y'] >= 0)
    ]
    
    print(f"Final dataset size: {len(df)} records")
    return df

def feature_engineering(df):
    """Create additional features"""
    print("Engineering features...")
    
    # Create aspect ratio feature
    df['aspect_ratio'] = df['bbox_width'] / df['bbox_height']
    
    # Create area feature
    df['bbox_area'] = df['bbox_width'] * df['bbox_height']
    
    # Create color count feature
    color_cols = [col for col in df.columns if col.startswith('color_')]
    df['color_count'] = df[color_cols].sum(axis=1)
    
    # Create pattern count feature
    pattern_cols = [col for col in df.columns if col.startswith('pattern_')]
    df['pattern_count'] = df[pattern_cols].sum(axis=1)
    
    # Create total attributes feature
    attribute_cols = [col for col in df.columns if any(
        col.startswith(prefix) for prefix in ['color_', 'pattern_', 'sleeve_', 'neckline_', 'length_', 'fit_']
    )]
    df['total_attributes'] = df[attribute_cols].sum(axis=1)
    
    print(f"Added {5} engineered features")
    return df

def encode_categorical_features(df):
    """Encode categorical features"""
    print("Encoding categorical features...")
    
    # Label encode category
    le_category = LabelEncoder()
    df['category_encoded'] = le_category.fit_transform(df['category'])
    
    # Save label encoder
    encoders = {'category': le_category}
    
    return df, encoders

def normalize_numerical_features(df):
    """Normalize numerical features"""
    print("Normalizing numerical features...")
    
    numerical_cols = ['bbox_x', 'bbox_y', 'bbox_width', 'bbox_height', 
                     'aspect_ratio', 'bbox_area', 'color_count', 'pattern_count', 'total_attributes']
    
    scaler = StandardScaler()
    df[numerical_cols] = scaler.fit_transform(df[numerical_cols])
    
    return df, scaler

def split_data(df, config):
    """Split data into train, validation, and test sets"""
    print("Splitting data...")
    
    # Prepare features and target
    feature_cols = [col for col in df.columns if col not in ['image_id', 'category']]
    X = df[feature_cols]
    y = df['category_encoded']
    
    # First split: train+val vs test
    test_size = config['preprocessing']['test_split']
    val_size = config['preprocessing']['validation_split']
    random_state = config['preprocessing']['random_seed']
    
    X_temp, X_test, y_temp, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Second split: train vs val
    val_size_adjusted = val_size / (1 - test_size)
    X_train, X_val, y_train, y_val = train_test_split(
        X_temp, y_temp, test_size=val_size_adjusted, random_state=random_state, stratify=y_temp
    )
    
    print(f"Train set: {len(X_train)} samples")
    print(f"Validation set: {len(X_val)} samples")
    print(f"Test set: {len(X_test)} samples")
    
    return (X_train, X_val, X_test), (y_train, y_val, y_test)

def save_processed_data(data_splits, target_splits, encoders, scaler, config):
    """Save processed data and preprocessing objects"""
    processed_path = Path(config['dataset']['processed_data_path'])
    
    X_train, X_val, X_test = data_splits
    y_train, y_val, y_test = target_splits
    
    # Save data splits
    X_train.to_csv(processed_path / 'X_train.csv', index=False)
    X_val.to_csv(processed_path / 'X_val.csv', index=False)
    X_test.to_csv(processed_path / 'X_test.csv', index=False)
    
    pd.Series(y_train).to_csv(processed_path / 'y_train.csv', index=False, header=['category_encoded'])
    pd.Series(y_val).to_csv(processed_path / 'y_val.csv', index=False, header=['category_encoded'])
    pd.Series(y_test).to_csv(processed_path / 'y_test.csv', index=False, header=['category_encoded'])
    
    # Save preprocessing objects
    joblib.dump(encoders, processed_path / 'encoders.pkl')
    joblib.dump(scaler, processed_path / 'scaler.pkl')
    
    # Save feature names
    feature_names = list(X_train.columns)
    pd.Series(feature_names).to_csv(processed_path / 'feature_names.csv', index=False, header=['feature'])
    
    print(f"Saved processed data to {processed_path}")

def main():
    """Main preprocessing pipeline"""
    print("Starting data preprocessing...")
    
    # Load configuration
    config = load_config()
    
    # Load raw data
    df = load_raw_data(config)
    if df is None:
        print("No data to process. Please run download_dataset.py first.")
        return
    
    # Data cleaning
    df = clean_data(df)
    
    # Feature engineering
    df = feature_engineering(df)
    
    # Encode categorical features
    df, encoders = encode_categorical_features(df)
    
    # Normalize numerical features
    df, scaler = normalize_numerical_features(df)
    
    # Split data
    data_splits, target_splits = split_data(df, config)
    
    # Save processed data
    save_processed_data(data_splits, target_splits, encoders, scaler, config)
    
    print("\n✓ Data preprocessing completed successfully!")
    print("- Data cleaned and validated")
    print("- Features engineered and encoded")
    print("- Data split into train/val/test sets")
    print("- Preprocessing objects saved")

if __name__ == "__main__":
    main()
